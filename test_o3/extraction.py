# ===================================================================
# Prior Authorization Data Extractor for Google Colab
# Complete working version with enhanced prompt and robust JSON parsing
# ===================================================================

# Install required libraries
import subprocess
import sys

def install_requirements():
    """Install required packages for Colab"""
    try:
        import google.generativeai as genai
        print("✅ google-generativeai already installed")
    except ImportError:
        print("📦 Installing google-generativeai...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "google-generativeai"])
        print("✅ Installation completed")

install_requirements()

# Now import everything
import os
import json
import time
import re
from pathlib import Path
from typing import Optional, Dict, Any
import google.generativeai as genai
from google.generativeai.types import HarmCategory, HarmBlockThreshold

# ===================================================================
# ENHANCED PA EXTRACTION PROMPT
# ===================================================================

SYSTEM_PROMPT = """# Google Gemini System Prompt: Enhanced Prior Authorization Data Extraction

## **ROLE & CONTEXT**
You are an expert medical data extraction specialist analyzing healthcare referral packages to extract information for Prior Authorization (PA) forms. You process complex, multi-page documents that may include clinical notes, lab reports, imaging studies, demographics, and insurance information.

**WORKFLOW CONTEXT**: Your extracted data will be used to automatically fill PDF forms with interactive widgets (AcroForm fields). The goal is to extract accurate, structured data that can be mapped to specific form fields while understanding conditional logic and mutually exclusive options in PA forms.

**OCR INPUT EXPECTATION**: Referral packages are high-resolution scanned images combined into PDFs requiring OCR processing, not standard PDF text extraction.

## **DOCUMENT CHALLENGES TO EXPECT**
- **Layout complexity**: Mixed structured forms, unstructured clinical notes, tables, and checkboxes
- **Quality issues**: Scanned documents, fax artifacts, handwritten notes, poor image quality
- **Content variability**: Different hospital systems, provider documentation styles, medical abbreviations
- **Template vs. data confusion**: Distinguishing between form labels and actual patient data
- **Multi-modal content**: Text, tables, signatures, medical images, stamps
- **Dense code clustering**: Overlapping procedure codes, modifiers, and annotations
- **Multi-column layouts**: Packed data fields with spatial parsing challenges
- **Form variants**: Different suppliers, template versions, and layout modifications

## **REFERRAL-SPECIFIC PROCESSING**
- **Multi-Provider Coordination**: Extract care coordination details between referring providers, specialists, and infusion centers
- **Complex Dosing Calculations**: Handle mg/m2 calculations, BSA adjustments, and weight-based dosing 
- **Biosimilar Medications**: Identify when biosimilar alternatives are mentioned (e.g., rituximab vs riTUXimab-abbs)
- **Patient Name Consistency**: Handle variations in patient name formats across documents and validate using MRN
- **Infusion Scheduling**: Extract complex scheduling requirements and facility coordination details
- **Insurance Authorization Workflows**: Identify prior authorization status and approval processes

## **PA FORM FILLING LOGIC AWARENESS**

### **Conditional Field Logic**
- **Mutually Exclusive Options**: Understand that PA forms contain either/or choices (e.g., "New Patient" vs "Existing Patient")
- **Branching Paths**: Some sections become relevant only based on previous selections
- **Conditional Dependencies**: Certain fields should only be completed if specific criteria are met
- **Drug-Specific Requirements**: Different medications have different approval criteria and required evidence

### **Form Completion Strategy**
- **Selective Filling**: Only extract data for fields that should logically be completed based on patient situation
- **Checkbox Logic**: For multiple choice sections, identify the most appropriate single option rather than checking multiple boxes
- **Required vs Optional**: Prioritize extraction of mandatory fields over optional supplementary information
- **Evidence Hierarchy**: Focus on strongest clinical evidence that supports the PA request

## **EXTRACTION TASK**
Extract all relevant information from the provided referral package that would be needed to complete a Prior Authorization form. For each piece of information, provide your reasoning process, cite the source location, and include spatial validation where applicable.

## **OUTPUT FORMAT**
Return a JSON object with the following structure, including reasoning and citations for each field:

```json
{
  "extraction_metadata": {
    "total_pages_processed": "number",
    "document_quality_assessment": "excellent|good|fair|poor",
    "primary_document_types_identified": ["clinical_notes", "lab_results", "demographics", "insurance_card", "etc"],
    "form_types_detected": ["CMS-1500", "NCPDP", "UB-04", "custom"],
    "spatial_processing_applied": "boolean",
    "extraction_confidence_overall": "number (0.0-1.0)"
  },
  
  "tier_1_mandatory_fields": {
    "patient_demographics": {
      "full_name": {
        "value": "extracted name or null",
        "reasoning": "Step-by-step thought process of how you found this",
        "citations": ["page X, section Y", "document header", "etc"],
        "confidence": "number (0.0-1.0)",
        "validation_notes": "any concerns about accuracy"
      },
      "date_of_birth": {
        "value": "YYYY-MM-DD format or null",
        "reasoning": "How you identified and formatted the DOB",
        "citations": ["page X, demographics section"],
        "confidence": "number",
        "validation_notes": "format consistency, multiple sources confirmation"
      },
      "address": {
        "value": "full address or null",
        "reasoning": "How you pieced together the complete address",
        "citations": ["page X, patient info section"],
        "confidence": "number",
        "validation_notes": "completeness, formatting issues"
      },
      "phone_numbers": {
        "value": ["primary phone", "secondary phone"],
        "reasoning": "How you distinguished between different phone types",
        "citations": ["page X, contact info"],
        "confidence": "number",
        "validation_notes": "format validation, type identification"
      },
      "medical_record_number": {
        "value": "MRN or null",
        "reasoning": "How you identified the MRN vs other ID numbers",
        "citations": ["page X, header section"],
        "confidence": "number",
        "validation_notes": "distinguished from other IDs"
      },
    
"physical_measurements": {
    "weight": {"value": "in lbs and kg", "required_for": "dosing calculations"},
    "height": {"value": "in inches and cm", "required_for": "BMI/BSA calculations"},
    "bmi": {"value": "calculated or stated"},
    "bsa": {"value": "body surface area if mentioned"}
}
    },
    
    "insurance_information": {
      "primary_insurance": {
        "member_id": {
          "value": "member ID or null",
          "reasoning": "How you distinguished member ID from group numbers, policy numbers",
          "citations": ["insurance card image", "billing section"],
          "confidence": "number",
          "validation_notes": "format validation, spatial extraction applied"
        },
        "payer_name": {
          "value": "insurance company name or null",
          "reasoning": "How you identified the primary payer",
          "citations": ["insurance documentation"],
          "confidence": "number",
          "validation_notes": "plan vs payer distinction"
        },
        "group_number": {
          "value": "group number or null",
          "reasoning": "How you found group vs member ID",
          "citations": ["insurance card"],
          "confidence": "number",
          "validation_notes": "format validation"
        },
        "plan_type": {
          "value": "plan description or null",
          "reasoning": "How you identified plan type/description",
          "citations": ["coverage page"],
          "confidence": "number",
          "validation_notes": "completeness"
        }
      }
    },
    
    "prescriber_information": {
      "physician_name": {
        "value": "prescribing physician name or null",
        "reasoning": "How you identified the prescribing physician vs other providers",
        "citations": ["signature block", "provider section"],
        "confidence": "number",
        "validation_notes": "distinguished from other providers"
      },
      "npi_number": {
        "value": "NPI number or null",
        "reasoning": "How you identified NPI vs other provider numbers",
        "citations": ["provider info section"],
        "confidence": "number",
        "validation_notes": "10-digit format validation"
      },
      "facility_name": {
        "value": "prescribing facility name or null",
        "reasoning": "How you identified the prescribing facility",
        "citations": ["letterhead", "facility section"],
        "confidence": "number",
        "validation_notes": "distinguished from other facilities"
      },
      "facility_address": {
        "value": "facility address or null",
        "reasoning": "How you found the complete facility address",
        "citations": ["header", "contact info"],
        "confidence": "number",
        "validation_notes": "completeness"
      },
      "phone_fax": {
        "value": {"phone": "number", "fax": "number"},
        "reasoning": "How you distinguished phone vs fax numbers",
        "citations": ["contact section"],
        "confidence": "number",
        "validation_notes": "format validation"
      },
      "coordinated_care": {
        "value": "multi-provider coordination details or null",
        "reasoning": "How you identified care coordination between providers",
        "citations": ["provider communications", "referral notes"],
        "confidence": "number",
        "validation_notes": "provider roles, scheduling coordination"
      },
      "infusion_facility_details": {
        "value": "infusion center information or null", 
        "reasoning": "How you identified where treatment will be administered",
        "citations": ["facility communications", "scheduling notes"],
        "confidence": "number",
        "validation_notes": "facility capabilities, location preferences"
      }
    }
  },
  
  "tier_2_clinical_justification": {
    "primary_diagnosis": {
      "icd_code": {
        "value": "ICD-10 code or null",
        "reasoning": "How you identified the primary diagnosis code",
        "citations": ["discharge summary", "billing codes"],
        "confidence": "number",
        "validation_notes": "ICD-10 format validation"
      },
      "diagnosis_description": {
        "value": "diagnosis description or null",
        "reasoning": "How you extracted the diagnosis description",
        "citations": ["clinical notes", "discharge summary"],
        "confidence": "number",
        "validation_notes": "completeness, medical terminology"
      },
      "severity_indicators": {
        "value": ["mild", "moderate", "severe", "refractory"],
        "reasoning": "How you assessed disease severity from clinical notes",
        "citations": ["progress notes", "assessment section"],
        "confidence": "number",
        "validation_notes": "clinical interpretation"
      }
    },
    
    "requested_medication": {
      "drug_name": {
        "value": "medication name or null",
        "reasoning": "How you identified the requested medication",
        "citations": ["medication orders", "therapy plan"],
        "confidence": "number",
        "validation_notes": "brand vs generic, spelling",
        "biosimilar_info": "include biosimilar alternatives if mentioned"
      },
      "ndc_number": {
        "value": "NDC code or null",
        "reasoning": "How you extracted and validated NDC format",
        "citations": ["prescription section"],
        "confidence": "number",
        "validation_notes": "verified NDC format: XXXXX-XXXX-XX or 11 digits"
      },
      "complex_dosing": {
        "value": "detailed dosing calculation or null",
        "reasoning": "How you extracted mg/m2 calculations and BSA adjustments",
        "citations": ["infusion orders", "therapy plan"],
        "confidence": "number",
        "validation_notes": "BSA calculations, weight-based dosing"
      },
      "dosage": {
        "value": "dose amount or null",
        "reasoning": "How you extracted the dosage information",
        "citations": ["medication orders"],
        "confidence": "number",
        "validation_notes": "units, calculation accuracy"
      },
      "frequency": {
        "value": "dosing frequency or null",
        "reasoning": "How you determined the dosing schedule",
        "citations": ["therapy plan", "orders"],
        "confidence": "number",
        "validation_notes": "schedule clarity"
      },
      "route": {
        "value": "administration route or null",
        "reasoning": "How you identified the route of administration",
        "citations": ["medication orders"],
        "confidence": "number",
        "validation_notes": "route abbreviation interpretation"
      }
    },
    
    "treatment_history": {
      "previous_medications": {
        "value": [{"drug": "name", "dates": "range", "response": "outcome"}],
        "reasoning": "How you extracted the treatment timeline and outcomes",
        "citations": ["progress notes", "medication history"],
        "confidence": "number",
        "validation_notes": "timeline accuracy, response assessment"
      },
      "treatment_failures": {
        "value": [{"treatment": "name", "reason": "failure reason", "date": "when"}],
        "reasoning": "How you identified treatment failures and reasons",
        "citations": ["clinical notes", "provider assessment"],
        "confidence": "number",
        "validation_notes": "failure reasoning clarity"
      },
      "contraindications": {
        "value": ["contraindication list"],
        "reasoning": "How you identified contraindications to standard treatments",
        "citations": ["allergy section", "clinical notes"],
        "confidence": "number",
        "validation_notes": "medical accuracy"
      }
    }
  },
  
  "tier_3_supporting_data": {
    "laboratory_results": {
      "recent_labs": {
        "value": [{"test": "name", "result": "value", "date": "when", "reference_range": "normal range"}],
        "reasoning": "How you extracted relevant lab values and dates",
        "citations": ["lab reports", "cumulative reports"],
        "confidence": "number",
        "validation_notes": "result accuracy, date correlation"
      },
      "biomarkers": {
        "value": [{"marker": "name", "level": "value", "significance": "elevated/normal/low"}],
        "reasoning": "How you identified disease-specific biomarkers",
        "citations": ["specialty lab results"],
        "confidence": "number",
        "validation_notes": "clinical relevance"
      }
    },
    
    "imaging_studies": {
      "studies_performed": {
        "value": [{"type": "MRI/CT/etc", "date": "when", "findings": "key findings"}],
        "reasoning": "How you extracted imaging information and key findings",
        "citations": ["radiology reports", "imaging summaries"],
        "confidence": "number",
        "validation_notes": "finding accuracy, clinical relevance"
      }
    },
    
    "clinical_assessment": {
      "symptom_severity": {
        "value": "assessment of current symptoms or null",
        "reasoning": "How you assessed symptom severity from clinical documentation",
        "citations": ["progress notes", "patient assessment"],
        "confidence": "number",
        "validation_notes": "subjective vs objective measures"
      },
      "functional_status": {
        "value": "functional assessment or null",
        "reasoning": "How you determined patient's functional capacity",
        "citations": ["clinical notes", "physical therapy notes"],
        "confidence": "number",
        "validation_notes": "assessment completeness"
      }
    }
  },
  
  "clinical_narrative": {
    "medical_necessity_story": {
      "value": "synthesized clinical narrative explaining why treatment is needed or null",
      "reasoning": "How you constructed the medical necessity argument from available data",
      "citations": ["multiple sources combined"],
      "confidence": "number",
      "validation_notes": "narrative coherence, medical logic"
    },
    "treatment_timeline": {
      "value": "chronological sequence of treatments and responses or null",
      "reasoning": "How you constructed the treatment timeline",
      "citations": ["multiple progress notes"],
      "confidence": "number",
      "validation_notes": "chronological accuracy"
    }
  },
  
  "missing_critical_information": {
    "fields_not_found": ["list of important fields that could not be extracted"],
    "potential_sources": ["suggestions for where missing info might be found"],
    "impact_assessment": "how missing information affects PA likelihood",
    "required_vs_optional": {
      "missing_required_fields": ["list of mandatory PA fields with no data found"],
      "missing_optional_fields": ["list of supplementary fields with no data found"],
      "critical_gaps": ["fields whose absence significantly weakens PA case"],
      "recommended_actions": ["specific steps to obtain missing required information"]
    }
  },
  
  "data_quality_issues": {
    "illegible_sections": ["descriptions of unclear or illegible parts"],
    "ambiguous_data": ["fields where interpretation was uncertain"],
    "conflicting_information": ["instances where different documents had conflicting info"],
    "format_validation_failures": ["codes or data that failed validation checks"]
  }
}
```

Now, analyze the provided referral package and extract all relevant PA form information following this enhanced structured approach with spatial processing, medical code validation, and PA form filling workflow awareness."""

# ===================================================================
# PA DATA EXTRACTOR CLASS WITH ROBUST JSON PARSING
# ===================================================================

class PADataExtractor:
    """Prior Authorization Data Extractor using Gemini API with robust JSON parsing"""
    
    def __init__(self, api_key: str, model_name: str = "gemini-2.0-flash"):
        """
        Initialize the PA Data Extractor
        
        Args:
            api_key: Google AI Studio API key
            model_name: Gemini model to use
        """
        genai.configure(api_key=api_key)
        self.model_name = model_name
        self.model = genai.GenerativeModel(
            model_name=model_name,
            system_instruction=SYSTEM_PROMPT
        )
        
        # Safety settings to allow medical content
        self.safety_settings = {
            HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_NONE,
            HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_NONE,
            HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_NONE,
            HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_NONE,
        }
        
        # Generation config for structured output
        self.generation_config = genai.types.GenerationConfig(
            temperature=0.1,  # Low temperature for consistent extraction
            top_p=0.8,
            top_k=40,
            max_output_tokens=8192,
            response_mime_type="application/json"
        )
    
    def upload_file(self, file_path: str, display_name: Optional[str] = None):
        """
        Upload a file to Gemini using the File API
        
        Args:
            file_path: Path to the file to upload
            display_name: Optional display name for the file
            
        Returns:
            Uploaded file object
        """
        file_path = Path(file_path)
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        if display_name is None:
            display_name = file_path.name
        
        print(f"📤 Uploading file: {file_path}")
        print(f"📊 File size: {file_path.stat().st_size / (1024*1024):.2f} MB")
        
        # Upload file
        uploaded_file = genai.upload_file(
            path=str(file_path),
            display_name=display_name
        )
        
        print(f"✅ Upload completed. File URI: {uploaded_file.uri}")
        
        # Wait for file to be processed
        print("⏳ Waiting for file processing...")
        while uploaded_file.state.name == "PROCESSING":
            print("   🔄 Processing...")
            time.sleep(2)
            uploaded_file = genai.get_file(uploaded_file.name)
        
        if uploaded_file.state.name == "FAILED":
            raise ValueError(f"File processing failed: {uploaded_file.error}")
        
        print("🎉 File processing completed successfully!")
        return uploaded_file
    
    def extract_json_from_text(self, text: str) -> Optional[Dict[str, Any]]:
        """
        Extract JSON from text using multiple strategies
        
        Args:
            text: Text containing JSON data
            
        Returns:
            Parsed JSON dict or None if extraction failed
        """
        # Strategy 1: Try direct parsing
        try:
            return json.loads(text)
        except json.JSONDecodeError:
            pass
        
        # Strategy 2: Find and extract the first valid JSON object using bracket matching
        try:
            start_idx = text.find('{')
            if start_idx != -1:
                bracket_count = 0
                end_idx = start_idx
                
                for i in range(start_idx, len(text)):
                    if text[i] == '{':
                        bracket_count += 1
                    elif text[i] == '}':
                        bracket_count -= 1
                        if bracket_count == 0:
                            end_idx = i + 1
                            break
                
                json_text = text[start_idx:end_idx]
                return json.loads(json_text)
        except Exception:
            pass
        
        # Strategy 3: Remove markdown code blocks and try again
        try:
            cleaned_text = text
            if "```json" in cleaned_text:
                cleaned_text = cleaned_text.split("```json")[1].split("```")[0]
            elif "```" in cleaned_text:
                cleaned_text = cleaned_text.split("```")[1].split("```")[0]
            
            cleaned_text = cleaned_text.strip()
            return json.loads(cleaned_text)
        except Exception:
            pass
        
        # Strategy 4: Use regex to find JSON object
        try:
            # Look for content between outermost { and }
            match = re.search(r'\{(?:[^{}]|(?:\{[^{}]*\}))*\}', text, re.DOTALL)
            if match:
                json_text = match.group(0)
                # Recursively expand to capture nested objects
                while True:
                    expanded = re.search(r'\{(?:[^{}]|(?:\{(?:[^{}]|(?:\{[^{}]*\}))*\}))*\}', text, re.DOTALL)
                    if expanded and len(expanded.group(0)) > len(json_text):
                        json_text = expanded.group(0)
                    else:
                        break
                return json.loads(json_text)
        except Exception:
            pass
        
        return None
    
    def extract_pa_data(self, referral_pdf_path: str, patient_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Extract PA data from referral package PDF with robust JSON parsing
        
        Args:
            referral_pdf_path: Path to the referral package PDF
            patient_id: Optional patient identifier for tracking
            
        Returns:
            Extracted PA data as dictionary
        """
        try:
            # Upload the referral package
            uploaded_file = self.upload_file(referral_pdf_path, f"Referral_Package_{patient_id or 'Unknown'}")
            
            # Create prompt for extraction
            prompt = f"""
            Please analyze this referral package PDF and extract all relevant information for Prior Authorization form completion.
            
            Patient ID: {patient_id or 'Not provided'}
            
            Focus on:
            1. Complete demographic and insurance information
            2. Clinical justification and diagnosis details  
            3. Requested medication and treatment history
            4. Supporting laboratory and imaging data
            5. Medical necessity narrative
            6. Provider coordination and infusion facility details
            7. Complex dosing calculations and biosimilar information
            
            Return the extracted data in the specified JSON format with detailed reasoning, citations, and confidence scores for each field.
            """
            
            print("🧠 Sending extraction request to Gemini...")
            
            # Generate content with the uploaded file
            response = self.model.generate_content(
                [uploaded_file, prompt],
                generation_config=self.generation_config,
                safety_settings=self.safety_settings
            )
            
            if not response.text:
                raise ValueError("Empty response from Gemini API")
            
            # Try to extract JSON using multiple strategies
            extracted_data = self.extract_json_from_text(response.text)
            
            if extracted_data is None:
                print("❌ Failed to extract JSON from response")
                print("🔄 Trying alternative extraction method...")
                return self.extract_pa_data_alternative(referral_pdf_path, patient_id)
            
            print("✅ Successfully extracted JSON data")
            
            # Add metadata
            extracted_data["processing_metadata"] = {
                "patient_id": patient_id,
                "file_uri": uploaded_file.uri,
                "model_used": self.model_name,
                "processing_time": time.time(),
                "file_size_mb": Path(referral_pdf_path).stat().st_size / (1024*1024),
                "extraction_method": "standard"
            }
            
            return extracted_data
            
        except Exception as e:
            print(f"❌ Error during extraction: {e}")
            print("🔄 Trying alternative extraction method...")
            return self.extract_pa_data_alternative(referral_pdf_path, patient_id)
    
    def extract_pa_data_alternative(self, referral_pdf_path: str, patient_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Alternative extraction method without JSON mime type constraint
        """
        try:
            # Upload the referral package
            uploaded_file = self.upload_file(referral_pdf_path, f"Referral_Package_{patient_id or 'Unknown'}")
            
            # Modified generation config without JSON mime type
            alt_generation_config = genai.types.GenerationConfig(
                temperature=0.1,
                top_p=0.8,
                top_k=40,
                max_output_tokens=8192,
                # Remove response_mime_type to allow text response
            )
            
            # Create prompt that explicitly asks for JSON
            prompt = f"""
            Please analyze this referral package PDF and extract all relevant information for Prior Authorization form completion.
            
            Patient ID: {patient_id or 'Not provided'}
            
            IMPORTANT: Return ONLY a valid JSON object. Do not include any text before or after the JSON.
            The JSON should start with {{ and end with }}.
            
            Focus on:
            1. Complete demographic and insurance information
            2. Clinical justification and diagnosis details  
            3. Requested medication and treatment history
            4. Supporting laboratory and imaging data
            5. Medical necessity narrative
            
            Return the extracted data following the exact JSON structure specified in your instructions.
            """
            
            print("🧠 Sending extraction request to Gemini (alternative method)...")
            
            # Generate content with the uploaded file
            response = self.model.generate_content(
                [uploaded_file, prompt],
                generation_config=alt_generation_config,
                safety_settings=self.safety_settings
            )
            
            if not response.text:
                raise ValueError("Empty response from Gemini API")
            
            # Extract JSON from response
            extracted_data = self.extract_json_from_text(response.text)
            
            if extracted_data is None:
                # Last resort: save raw response for manual inspection
                debug_file = f"pa_debug_response_{patient_id}.txt"
                with open(debug_file, 'w', encoding='utf-8') as f:
                    f.write(response.text)
                print(f"💾 Raw response saved to: {debug_file}")
                
                return {
                    "error": "Failed to parse JSON response", 
                    "raw_response": response.text[:2000] + "..." if len(response.text) > 2000 else response.text,
                    "debug_file": debug_file
                }
            
            print("✅ Successfully extracted JSON data (alternative method)")
            
            # Add metadata
            extracted_data["processing_metadata"] = {
                "patient_id": patient_id,
                "file_uri": uploaded_file.uri,
                "model_used": self.model_name,
                "processing_time": time.time(),
                "file_size_mb": Path(referral_pdf_path).stat().st_size / (1024*1024),
                "extraction_method": "alternative"
            }
            
            return extracted_data
            
        except Exception as e:
            print(f"❌ Error during alternative extraction: {e}")
            return {"error": str(e)}
    
    def save_results(self, extracted_data: Dict[str, Any], output_path: str) -> None:
        """
        Save extracted data to JSON file
        
        Args:
            extracted_data: Extracted PA data
            output_path: Path to save the results
        """
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(extracted_data, f, indent=2, ensure_ascii=False)
        
        print(f"💾 Results saved to: {output_path}")
    
    def generate_missing_data_report(self, extracted_data: Dict[str, Any]) -> str:
        """
        Generate a human-readable missing data report
        
        Args:
            extracted_data: Extracted PA data
            
        Returns:
            Formatted missing data report
        """
        if "missing_critical_information" not in extracted_data:
            return "No missing data information available."
        
        missing_info = extracted_data["missing_critical_information"]
        
        report = "# Prior Authorization Missing Data Report\n\n"
        
        if "required_vs_optional" in missing_info:
            req_opt = missing_info["required_vs_optional"]
            
            if req_opt.get("missing_required_fields"):
                report += "## ⚠️ Missing Required Fields\n"
                for field in req_opt["missing_required_fields"]:
                    report += f"- {field}\n"
                report += "\n"
            
            if req_opt.get("critical_gaps"):
                report += "## 🚨 Critical Gaps\n"
                for gap in req_opt["critical_gaps"]:
                    report += f"- {gap}\n"
                report += "\n"
            
            if req_opt.get("recommended_actions"):
                report += "## 📋 Recommended Actions\n"
                for action in req_opt["recommended_actions"]:
                    report += f"- {action}\n"
                report += "\n"
        
        if missing_info.get("impact_assessment"):
            report += f"## 📊 Impact Assessment\n{missing_info['impact_assessment']}\n\n"
        
        return report

# ===================================================================
# EASY-TO-USE FUNCTIONS FOR COLAB
# ===================================================================

def process_referral_pdf(api_key: str, pdf_path: str, patient_id: str = "COLAB_TEST"):
    """
    🚀 Main function to process a referral PDF in Google Colab
    
    Args:
        api_key: Your Gemini API key from https://aistudio.google.com/app/apikey
        pdf_path: Path to your uploaded PDF file
        patient_id: Patient identifier for tracking
        
    Returns:
        Dictionary with extracted PA data
    """
    print("🏥 Starting Prior Authorization Data Extraction...")
    print("=" * 60)
    
    # Initialize extractor
    extractor = PADataExtractor(api_key)
    
    # Extract data
    print(f"📁 Processing file: {pdf_path}")
    results = extractor.extract_pa_data(pdf_path, patient_id)
    
    # Save results
    output_file = f"pa_results_{patient_id}.json"
    extractor.save_results(results, output_file)
    
    # Generate report
    report = extractor.generate_missing_data_report(results)
    report_file = f"pa_report_{patient_id}.md"
    with open(report_file, 'w') as f:
        f.write(report)
    print(f"📋 Report saved to: {report_file}")
    
    # Show summary
    print("\n" + "=" * 60)
    if "error" not in results:
        metadata = results.get("extraction_metadata", {})
        print("✅ EXTRACTION COMPLETED SUCCESSFULLY!")
        print(f"📄 Pages processed: {metadata.get('total_pages_processed', 'Unknown')}")
        print(f"🎯 Overall confidence: {metadata.get('extraction_confidence_overall', 'Unknown')}")
        print(f"📋 Document quality: {metadata.get('document_quality_assessment', 'Unknown')}")
        print(f"🔧 Extraction method: {metadata.get('extraction_method', 'Unknown')}")
        print(f"💾 Results file: {output_file}")
        print(f"📋 Report file: {report_file}")
        
        # Show key findings
        if "tier_1_mandatory_fields" in results:
            patient_info = results["tier_1_mandatory_fields"].get("patient_demographics", {})
            if patient_info.get("full_name", {}).get("value"):
                print(f"👤 Patient: {patient_info['full_name']['value']}")
            if patient_info.get("medical_record_number", {}).get("value"):
                print(f"🏥 MRN: {patient_info['medical_record_number']['value']}")
        
    else:
        print("❌ EXTRACTION FAILED")
        print(f"Error: {results['error']}")
        if "debug_file" in results:
            print(f"🔍 Check debug file for raw response: {results['debug_file']}")
    
    print("=" * 60)
    return results

def upload_and_process(api_key: str, patient_id: str = "COLAB_TEST"):
    """
    🔄 Upload a file and process it in one step
    
    Args:
        api_key: Your Gemini API key
        patient_id: Patient identifier
    """
    from google.colab import files
    
    print("📤 Please upload your referral package PDF...")
    uploaded = files.upload()
    
    if not uploaded:
        print("❌ No file uploaded!")
        return None
    
    pdf_filename = list(uploaded.keys())[0]
    print(f"✅ File uploaded: {pdf_filename}")
    
    # Process the uploaded file
    return process_referral_pdf(api_key, pdf_filename, patient_id)

def show_results_summary(results: Dict[str, Any]):
    """
    📊 Display a nice summary of extraction results
    
    Args:
        results: Results dictionary from process_referral_pdf
    """
    if "error" in results:
        print(f"❌ Error: {results['error']}")
        return
    
    print("📊 EXTRACTION RESULTS SUMMARY")
    print("=" * 50)
    
    # Patient info
    patient_data = results.get("tier_1_mandatory_fields", {}).get("patient_demographics", {})
    print("👤 PATIENT INFORMATION:")
    print(f"   Name: {patient_data.get('full_name', {}).get('value', 'Not found')}")
    print(f"   DOB: {patient_data.get('date_of_birth', {}).get('value', 'Not found')}")
    print(f"   MRN: {patient_data.get('medical_record_number', {}).get('value', 'Not found')}")
    
    # Insurance info
    insurance_data = results.get("tier_1_mandatory_fields", {}).get("insurance_information", {}).get("primary_insurance", {})
    print("\n💳 INSURANCE INFORMATION:")
    print(f"   Payer: {insurance_data.get('payer_name', {}).get('value', 'Not found')}")
    print(f"   Member ID: {insurance_data.get('member_id', {}).get('value', 'Not found')}")
    print(f"   Group #: {insurance_data.get('group_number', {}).get('value', 'Not found')}")
    
    # Clinical info
    clinical_data = results.get("tier_2_clinical_justification", {})
    diagnosis = clinical_data.get("primary_diagnosis", {})
    medication = clinical_data.get("requested_medication", {})
    
    print("\n🏥 CLINICAL INFORMATION:")
    print(f"   Diagnosis: {diagnosis.get('diagnosis_description', {}).get('value', 'Not found')}")
    print(f"   ICD Code: {diagnosis.get('icd_code', {}).get('value', 'Not found')}")
    print(f"   Medication: {medication.get('drug_name', {}).get('value', 'Not found')}")
    print(f"   Dosage: {medication.get('dosage', {}).get('value', 'Not found')}")
    
    # Quality metrics
    metadata = results.get("extraction_metadata", {})
    print(f"\n📈 QUALITY METRICS:")
    print(f"   Overall Confidence: {metadata.get('extraction_confidence_overall', 'Unknown')}")
    print(f"   Document Quality: {metadata.get('document_quality_assessment', 'Unknown')}")
    print(f"   Pages Processed: {metadata.get('total_pages_processed', 'Unknown')}")
    print(f"   Extraction Method: {metadata.get('extraction_method', 'Unknown')}")

# ===================================================================
# EXAMPLE USAGE FOR GOOGLE COLAB
# ===================================================================

def example_usage():
    """
    📚 Example of how to use this code in Google Colab
    """
    print(
   
    
   
    # API_KEY = "AIzaSyCy-XcH5KbuCRq5BmFRX-oaVfeYw58JTyU"  # Get from https://aistudio.google.com/app/apikey
    
    # # 2️⃣ Option A - Upload and process in one step:
    # # results = upload_and_process(API_KEY, "PATIENT001")
    
    # # 2️⃣ Option B - Process an already uploaded file:
    # results = process_referral_pdf(API_KEY, "/content/referral_package.pdf", "PATIENT001")
    
    
    # show_results_summary(results)
    
    
    # patient_name = results["tier_1_mandatory_fields"]["patient_demographics"]["full_name"]["value"]
    

    )

# Show usage instructions
print("🎉 PA Data Extractor with Robust JSON Parsing loaded successfully!")
print("📖 Run example_usage() to see how to use this code.")
example_usage()